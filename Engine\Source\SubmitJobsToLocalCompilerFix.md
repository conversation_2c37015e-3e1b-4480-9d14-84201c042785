# SubmitJobsToLocalCompiler函数断言失败修复文档

## 问题描述

在执行`Manager->AllJobs.SubmitJobs(JobsToRetry)`时出现断言失败错误：

```
Error: Assertion failed: Blocks.Num() == 0 || Blocks.Last().StrippedLineNum < StrippedLineNum 
[File:G:\5.5\UnrealEngine\Engine\Source\Runtime\RenderCore\Private\ShaderPreprocessTypes.cpp] [Line: 158]
```

## 根本原因分析

### 1. 断言失败的位置
错误发生在`ShaderPreprocessTypes.cpp`第158行，这是`FShaderDiagnosticRemapper::AddSourceBlock`方法中的断言检查：

```cpp
// 确保源代码块按照剥离后的行号顺序添加
check(Blocks.Num() == 0 || Blocks.Last().StrippedLineNum < StrippedLineNum);
```

### 2. 问题的根源
当我们将失败的着色器任务重新提交给本地编译器时，Job对象包含了之前BK分布式编译过程中产生的预处理数据：

- `PreprocessOutput`: 包含预处理结果和诊断信息重映射器
- `SecondaryPreprocessOutput`: 可能的二次预处理输出
- `SecondaryOutput`: 可能的二次编译输出

这些数据中的`FShaderDiagnosticRemapper`包含了源代码块（Blocks）信息，当本地编译器重新预处理着色器时，新的源代码块可能与旧的块信息冲突，导致行号顺序检查失败。

### 3. 具体触发机制
1. BK分布式编译失败的Job被标记为本地重试
2. Job对象仍然包含之前的预处理数据
3. 本地编译器开始重新预处理着色器
4. 新的预处理过程尝试添加源代码块
5. 由于旧的块信息仍然存在，行号顺序检查失败
6. 断言触发，程序崩溃

## 修复方案

### 1. 移除错误的Release()调用
原始代码中的`Job->Release()`调用是错误的，它可能导致Job对象状态不一致：

```cpp
// 错误的代码
Job->Release();  // 这会破坏Job对象的引用计数
```

### 2. 完整重置Job状态
正确的做法是重置所有相关的输出和预处理数据：

```cpp
// 重置基本状态
Job->bFinalized = false;
Job->bSucceeded = false;
Job->Priority = EShaderCompileJobPriority::ForceLocal;

// 清除输出数据
Job->Output = FShaderCompilerOutput();

// 重置预处理输出以避免陈旧数据
Job->PreprocessOutput = FShaderPreprocessOutput();
if (Job->SecondaryPreprocessOutput.IsValid())
{
    Job->SecondaryPreprocessOutput.Reset();
}
if (Job->SecondaryOutput.IsValid())
{
    Job->SecondaryOutput.Reset();
}
```

### 3. 保留输入数据
重要的是只重置输出相关的数据，而保留输入数据：
- `Job->Input`: 着色器编译输入，包含源代码和编译参数
- `Job->Key`: 着色器编译键值
- `Job->ShaderParameters`: 着色器参数

## 技术细节

### FShaderPreprocessOutput结构
这个结构包含：
- `Remapper`: `FShaderDiagnosticRemapper`对象，用于错误信息重映射
- `Errors`: 预处理错误列表
- `PragmaDirectives`: 编译指令列表
- `bSucceeded`: 预处理是否成功

### FShaderDiagnosticRemapper
包含源代码块信息：
- `Blocks`: 源代码块数组，按行号排序
- `StrippedLines`: 剥离后的行信息

当重新预处理时，如果不清除这些数据，新的块信息可能与旧的冲突。

### 内存管理
- `TUniquePtr<FShaderPreprocessOutput>`: 使用智能指针管理
- `Reset()`: 正确释放智能指针指向的对象
- 避免手动调用`Release()`破坏引用计数

## 修复效果

### 1. 消除断言失败
- ✅ 解决了`StrippedLineNum`顺序检查失败
- ✅ 避免了预处理数据冲突
- ✅ 确保本地重试能够正常进行

### 2. 提高系统稳定性
- 正确的Job状态重置
- 避免内存泄漏和悬挂指针
- 确保本地编译器能够从干净状态开始

### 3. 保持数据完整性
- 保留必要的输入数据
- 清除可能冲突的输出数据
- 正确管理智能指针生命周期

## 预防措施

### 1. 代码审查要点
- 检查所有Job状态重置的地方
- 确保预处理数据被正确清理
- 避免不必要的`Release()`调用

### 2. 测试建议
- 测试BK分布式编译失败后的本地重试
- 验证多次重试的稳定性
- 检查内存使用模式

### 3. 监控指标
- 本地重试成功率
- 断言失败频率
- 内存泄漏检测

## 相关代码位置

### 修复的函数
- `FShaderCompileBKDistThreadRunnable::SubmitJobsToLocalCompiler`

### 相关结构
- `FShaderCompileJob`
- `FShaderPreprocessOutput`
- `FShaderDiagnosticRemapper`
- `FShaderCompilerOutput`

### 断言位置
- `ShaderPreprocessTypes.cpp:158`
- `FShaderDiagnosticRemapper::AddSourceBlock`

## 总结

这个修复解决了BK分布式着色器编译失败后本地重试时的断言失败问题。通过正确重置Job的预处理输出数据，我们确保了：

1. **数据一致性**: 避免新旧预处理数据冲突
2. **内存安全**: 正确管理智能指针和引用计数
3. **系统稳定性**: 消除断言失败和崩溃
4. **功能完整性**: 保证本地重试机制正常工作

这个修复是BK分布式着色器编译器容错机制的重要组成部分，确保了当分布式编译失败时，系统能够可靠地回退到本地编译。
